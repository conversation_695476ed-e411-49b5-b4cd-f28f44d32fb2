<template>
    <div class="app-container statistics-container">
        <!-- 外部联系人数量卡片 -->
        <div class="statistics-card">
            <div class="card-header">
                <el-icon class="card-icon">
                    <User />
                </el-icon>
                <h3 class="card-title">外部联系人数量</h3>
            </div>

            <!-- 第一排：当前数量和跟昨日相比 -->
            <div class="stats-row">
                <div class="stat-item">
                    <div class="stat-label">当前数量</div>
                    <div class="stat-value current-count">{{ contactStats.currentCount }}</div>
                </div>
                <div class="stat-item">
                    <div class="stat-label">跟昨日相比</div>
                    <div class="stat-value" :class="contactStats.compareYesterday >= 0 ? 'positive' : 'negative'">
                        {{ contactStats.compareYesterday >= 0 ? '+' : '' }}{{ contactStats.compareYesterday }}
                    </div>
                </div>
            </div>

            <!-- 每个企业人数柱状图 -->
            <div class="chart-container">
                <div class="chart-title">每个企业人数</div>
                <div ref="companyCountChart" class="chart"></div>
            </div>
        </div>

        <!-- 发送信息数量和接收人发送次数卡片 -->
        <div class="cards-row">
            <div class="statistics-card half-width">
                <div class="card-header">
                    <el-icon class="card-icon">
                        <Message />
                    </el-icon>
                    <h3 class="card-title">发送信息数量</h3>
                </div>
                <div class="single-stat">
                    <div class="stat-value large">{{ messageStats.totalSent }}</div>
                    <div class="stat-label">条消息</div>
                </div>
            </div>

            <div class="statistics-card half-width">
                <div class="card-header">
                    <el-icon class="card-icon">
                        <ChatDotRound />
                    </el-icon>
                    <h3 class="card-title">接收人发送次数</h3>
                </div>
                <div class="single-stat">
                    <div class="stat-value large">{{ messageStats.receiverSendCount }}</div>
                    <div class="stat-label">次发送</div>
                </div>
            </div>
        </div>

        <!-- 不同属性推送次数和移除通讯录卡片 -->
        <div class="cards-row">
            <div class="statistics-card half-width">
                <div class="card-header">
                    <el-icon class="card-icon">
                        <PieChart />
                    </el-icon>
                    <h3 class="card-title">不同属性推送次数</h3>
                </div>
                <div ref="attributePushChart" class="chart"></div>
            </div>

            <div class="statistics-card half-width">
                <div class="card-header">
                    <el-icon class="card-icon">
                        <Delete />
                    </el-icon>
                    <h3 class="card-title">移除通讯录</h3>
                </div>
                <div ref="removeContactChart" class="chart"></div>
            </div>
        </div>
    </div>
</template>

<script setup name="Statistics">
import { ref, onMounted, nextTick } from 'vue'
import * as echarts from 'echarts'
import { User, Message, ChatDotRound, PieChart, Delete } from '@element-plus/icons-vue'

// 图表实例
const companyCountChart = ref(null)
const attributePushChart = ref(null)
const removeContactChart = ref(null)

let companyChart = null
let attributeChart = null
let removeChart = null

// 模拟数据
const contactStats = ref({
    currentCount: 1248,
    compareYesterday: 32
})

const messageStats = ref({
    totalSent: 5678,
    receiverSendCount: 892
})

// 企业人数数据（按人数从高到低排序）
const companyData = ref([
    { name: '华为技术有限公司', count: 156 },
    { name: '腾讯科技有限公司', count: 142 },
    { name: '阿里巴巴集团', count: 128 },
    { name: '百度在线网络技术', count: 115 },
    { name: '字节跳动科技', count: 98 },
    { name: '美团点评', count: 87 },
    { name: '京东集团', count: 76 },
    { name: '滴滴出行科技', count: 65 }
])

// 不同属性推送次数数据
const attributePushData = ref([
    { name: '行业', value: 1234 },
    { name: '主管部门', value: 856 },
    { name: '属地', value: 642 }
])

// 移除通讯录数据
const removeContactData = ref([
    { name: '企业删除', value: 45 },
    { name: '发改委删除', value: 23 }
])

// 初始化企业人数柱状图
const initCompanyChart = () => {
    if (!companyCountChart.value) return

    companyChart = echarts.init(companyCountChart.value)

    const option = {
        tooltip: {
            trigger: 'axis',
            axisPointer: {
                type: 'shadow'
            }
        },
        grid: {
            left: '3%',
            right: '4%',
            bottom: '3%',
            containLabel: true
        },
        xAxis: {
            type: 'category',
            data: companyData.value.map(item => item.name),
            axisLabel: {
                rotate: 45,
                fontSize: 10
            }
        },
        yAxis: {
            type: 'value',
            name: '人数'
        },
        series: [{
            name: '人数',
            type: 'bar',
            data: companyData.value.map(item => item.count),
            itemStyle: {
                color: '#409EFF'
            },
            barWidth: '60%'
        }]
    }

    companyChart.setOption(option)
}

// 初始化不同属性推送次数饼图
const initAttributeChart = () => {
    if (!attributePushChart.value) return

    attributeChart = echarts.init(attributePushChart.value)

    const option = {
        tooltip: {
            trigger: 'item',
            formatter: '{a} <br/>{b}: {c} ({d}%)'
        },
        legend: {
            orient: 'vertical',
            left: 'left'
        },
        series: [{
            name: '推送次数',
            type: 'pie',
            radius: '50%',
            data: attributePushData.value,
            emphasis: {
                itemStyle: {
                    shadowBlur: 10,
                    shadowOffsetX: 0,
                    shadowColor: 'rgba(0, 0, 0, 0.5)'
                }
            },
            itemStyle: {
                color: function (params) {
                    const colors = ['#409EFF', '#67C23A', '#E6A23C']
                    return colors[params.dataIndex]
                }
            }
        }]
    }

    attributeChart.setOption(option)
}

// 初始化移除通讯录饼图
const initRemoveChart = () => {
    if (!removeContactChart.value) return

    removeChart = echarts.init(removeContactChart.value)

    const option = {
        tooltip: {
            trigger: 'item',
            formatter: '{a} <br/>{b}: {c} ({d}%)'
        },
        legend: {
            orient: 'vertical',
            left: 'left'
        },
        series: [{
            name: '移除数量',
            type: 'pie',
            radius: '50%',
            data: removeContactData.value,
            emphasis: {
                itemStyle: {
                    shadowBlur: 10,
                    shadowOffsetX: 0,
                    shadowColor: 'rgba(0, 0, 0, 0.5)'
                }
            },
            itemStyle: {
                color: function (params) {
                    const colors = ['#F56C6C', '#E6A23C']
                    return colors[params.dataIndex]
                }
            }
        }]
    }

    removeChart.setOption(option)
}

// 窗口大小改变时重新调整图表
const handleResize = () => {
    companyChart?.resize()
    attributeChart?.resize()
    removeChart?.resize()
}

onMounted(() => {
    nextTick(() => {
        initCompanyChart()
        initAttributeChart()
        initRemoveChart()

        window.addEventListener('resize', handleResize)
    })
})

// 组件卸载时清理
import { onUnmounted } from 'vue'
onUnmounted(() => {
    window.removeEventListener('resize', handleResize)
    companyChart?.dispose()
    attributeChart?.dispose()
    removeChart?.dispose()
})
</script>

<style scoped lang="scss">
.statistics-container {
    padding: 20px;
    background-color: #f5f5f5;
    min-height: calc(100vh - 84px);
}

.statistics-card {
    background: #ffffff;
    border-radius: 8px;
    padding: 24px;
    margin-bottom: 20px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

    &.half-width {
        width: calc(50% - 10px);
    }
}

.card-header {
    display: flex;
    align-items: center;
    margin-bottom: 20px;

    .card-icon {
        font-size: 24px;
        color: #409EFF;
        margin-right: 12px;
    }

    .card-title {
        font-size: 18px;
        font-weight: bold;
        color: #303133;
        margin: 0;
    }
}

.stats-row {
    display: flex;
    gap: 40px;
    margin-bottom: 30px;
}

.stat-item {
    flex: 1;
    text-align: center;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 6px;

    .stat-label {
        font-size: 14px;
        color: #606266;
        margin-bottom: 8px;
    }

    .stat-value {
        font-size: 28px;
        font-weight: bold;
        color: #303133;

        &.current-count {
            color: #409EFF;
        }

        &.positive {
            color: #67C23A;
        }

        &.negative {
            color: #F56C6C;
        }

        &.large {
            font-size: 36px;
        }
    }
}

.chart-container {
    .chart-title {
        font-size: 16px;
        font-weight: 600;
        color: #303133;
        margin-bottom: 16px;
        text-align: center;
    }

    .chart {
        height: 300px;
        width: 100%;
    }
}

.cards-row {
    display: flex;
    gap: 20px;
    margin-bottom: 20px;
}

.single-stat {
    text-align: center;
    padding: 30px 20px;

    .stat-value {
        font-size: 48px;
        font-weight: bold;
        color: #409EFF;
        margin-bottom: 8px;

        &.large {
            font-size: 48px;
        }
    }

    .stat-label {
        font-size: 16px;
        color: #606266;
    }
}

.chart {
    height: 250px;
    width: 100%;
}

// 响应式设计
@media (max-width: 1200px) {
    .cards-row {
        flex-direction: column;

        .statistics-card.half-width {
            width: 100%;
        }
    }

    .stats-row {
        flex-direction: column;
        gap: 20px;
    }
}

@media (max-width: 768px) {
    .statistics-container {
        padding: 10px;
    }

    .statistics-card {
        padding: 16px;
    }

    .card-header {
        .card-title {
            font-size: 16px;
        }

        .card-icon {
            font-size: 20px;
        }
    }

    .stat-value {
        font-size: 24px !important;

        &.large {
            font-size: 32px !important;
        }
    }

    .chart {
        height: 200px;
    }

    .chart-container .chart {
        height: 250px;
    }
}
</style>